/*
This file is part of Telegram Desktop,
the official desktop application for the Telegram messaging service.

For license and copyright information please follow this link:
https://github.com/telegramdesktop/tdesktop/blob/master/LEGAL
*/
#pragma once

#include <QObject>

namespace Tests {

class TestMessageLogger : public QObject {
	Q_OBJECT

private slots:
	void testEscapeText();
	void testLogMacro();
	void testLoggedMessageStructure();
};

} // namespace Tests

/*
This file is part of Telegram Desktop,
the official desktop application for the Telegram messaging service.

For license and copyright information please follow this link:
https://github.com/telegramdesktop/tdesktop/blob/master/LEGAL
*/
#pragma once

#include "ui/layers/box_content.h"
#include "data/data_message_logger.h"
#include "history/history_item.h"

namespace Ui {
class VerticalLayout;
class FlatLabel;
} // namespace Ui

namespace Window {
class SessionController;
} // namespace Window

class MessageHistoryBox : public Ui::BoxContent {
public:
	MessageHistoryBox(
		QWidget*,
		not_null<Window::SessionController*> controller,
		FullMsgId messageId);

protected:
	void prepare() override;
	void setInnerFocus() override;

private:
	void setupContent();
	void addHistoryEntry(const Data::LoggedMessage &entry);
	QString formatEntryText(const Data::LoggedMessage &entry);
	QString formatDateTime(TimeId timestamp);

	const not_null<Window::SessionController*> _controller;
	const FullMsgId _messageId;
	QList<Data::LoggedMessage> _history;

	Ui::VerticalLayout *_content = nullptr;
};

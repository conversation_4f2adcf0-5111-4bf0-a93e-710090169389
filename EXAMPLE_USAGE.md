# Пример использования логирования сообщений

## Как это работает

После компиляции и запуска Telegram Desktop с добавленным логированием, все входящие сообщения будут автоматически записываться в два файла: `messages.html` и `messages.json` в папке документов пользователя.

**Новая функциональность**: Система также отслеживает редактирование и удаление сообщений, сохраняя оригинальный текст и временные метки всех изменений.

## Пример HTML файла (messages.html)

HTML файл создается с красивым оформлением, аналогичным экспорту Telegram:

```html
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Telegram Messages Log</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; background: #f5f5f5; }
    .page_body { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
    .message { border-bottom: 1px solid #eee; padding: 15px 0; }
    .message_header { color: #666; font-size: 13px; margin-bottom: 5px; }
    .message_text { color: #333; line-height: 1.4; }
    .service_message { font-style: italic; color: #888; }
    .outgoing { background: #e3f2fd; border-radius: 8px; padding: 10px; }
  </style>
</head>
<body>
  <div class="page_body">
    <h1>Telegram Messages Log</h1>

    <!-- Filter Controls -->
    <div class="filter_controls">
      <div class="filter_group">
        <label for="chat_filter">Filter by Chat:</label>
        <select id="chat_filter">
          <option value="">All Chats</option>
          <option value="Рабочий чат">Рабочий чат</option>
          <option value="Семейный чат">Семейный чат</option>
          <option value="Друзья">Друзья</option>
        </select>
      </div>
      <div class="filter_group">
        <label for="type_filter">Filter by Type:</label>
        <select id="type_filter">
          <option value="">All Types</option>
          <option value="message">Messages</option>
          <option value="edited_message">Edited</option>
          <option value="deleted_message">Deleted</option>
          <option value="service_message">Service</option>
        </select>
      </div>
      <div class="filter_group">
        <input type="text" id="search_filter" placeholder="Search in messages...">
      </div>
      <div class="filter_stats">
        <span id="message_count">4 messages</span>
      </div>
    </div>

    <div class="history" id="message_history">
      <div class="message" data-log-entry-id="log_1705317330_123456" data-chat-name="Рабочий чат" data-message-type="message">
        <div class="message_header">
          <strong>Рабочий чат</strong> (group_chat) - 2024-01-15 09:15:30 [ID: 98765] - From: Анна Петрова
        </div>
        <div class="message_text">
          Доброе утро! Готовы к совещанию?
        </div>
      </div>
      <div class="message outgoing" data-log-entry-id="log_1705317405_456789" data-chat-name="Семейный чат" data-message-type="message">
        <div class="message_header">
          <strong>Семейный чат</strong> (group_chat) - 2024-01-15 09:16:45 [ID: 98766] - From: Я
        </div>
        <div class="message_text">
          <div class="media_info">[photo]</div>
          Вот фото с отпуска!
        </div>
      </div>
      <div class="message edited_message" data-log-entry-id="log_1705317495_789012" data-chat-name="Рабочий чат" data-message-type="edited_message">
        <div class="message_header">
          <strong>Рабочий чат</strong> (group_chat) - 2024-01-15 09:17:30 [ID: 98765] - From: Анна Петрова <span class="edit_info">[EDITED at 2024-01-15 09:18:15]</span> <span class="related_info">(relates to: log_1705317330_123456)</span>
        </div>
        <div class="message_text">
          <div class="original_text"><strong>Original:</strong> Встреча в 15:00</div>
          <div class="edited_text"><strong>Edited to:</strong> Встреча перенесена на 16:00</div>
        </div>
      </div>
      <div class="message deleted_message" data-log-entry-id="log_1705317570_345678" data-chat-name="Друзья" data-message-type="deleted_message">
        <div class="message_header">
          <strong>Друзья</strong> (group_chat) - 2024-01-15 09:19:00 [ID: 98767] - From: Максим <span class="delete_info">[DELETED at 2024-01-15 09:19:30]</span> <span class="related_info">(relates to: log_1705317540_234567)</span>
        </div>
        <div class="message_text">
          <div class="deleted_info"><strong>Deleted message:</strong></div>
          <div class="deleted_content">Кто идет в кино сегодня вечером?</div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
```

## Пример JSON файла (messages.json)

JSON файл содержит структурированные данные, совместимые с форматом экспорта Telegram:

```json
{
  "about": "Telegram Desktop Messages Log",
  "export_date": "2024-01-15 09:15:00",
  "messages": [
    {
      "id": "98765",
      "message_id": "98765",
      "peer_id": "123456789",
      "log_entry_id": "log_1705317330_123456",
      "type": "message",
      "date": "2024-01-15 09:15:30",
      "date_unixtime": "1705317330",
      "chat": {
        "id": "123456789",
        "name": "Рабочий чат",
        "type": "group_chat"
      },
      "from": {
        "id": "987654321",
        "name": "Анна Петрова"
      },
      "text": "Доброе утро! Готовы к совещанию?",
      "out": false
    },
    {
      "id": "98766",
      "type": "message",
      "date": "2024-01-15 09:16:45",
      "date_unixtime": "1705317405",
      "chat": {
        "id": "123456789",
        "name": "Семейный чат",
        "type": "group_chat"
      },
      "from": {
        "id": "111222333",
        "name": "Я"
      },
      "text": "Вот фото с отпуска!",
      "media_type": "photo",
      "out": true
    },
    {
      "id": "98767",
      "type": "service",
      "date": "2024-01-15 09:17:12",
      "date_unixtime": "1705317432",
      "chat": {
        "id": "123456789",
        "name": "Друзья",
        "type": "group_chat"
      },
      "text": "Максим присоединился к группе",
      "out": false
    },
    {
      "id": "98765",
      "message_id": "98765",
      "peer_id": "123456789",
      "log_entry_id": "log_1705317495_789012",
      "related_log_entry_id": "log_1705317330_123456",
      "type": "message_edited",
      "date": "2024-01-15 09:17:30",
      "date_unixtime": "1705317450",
      "action": "edited",
      "action_date": "2024-01-15 09:18:15",
      "action_date_unixtime": "1705317495",
      "chat": {
        "id": "123456789",
        "name": "Рабочий чат",
        "type": "group_chat"
      },
      "from": {
        "id": "987654321",
        "name": "Анна Петрова"
      },
      "text": "Встреча перенесена на 16:00",
      "original_text": "Встреча в 15:00",
      "was_edited": true,
      "out": false
    },
    {
      "id": "98767",
      "message_id": "98767",
      "peer_id": "123456789",
      "log_entry_id": "log_1705317570_345678",
      "related_log_entry_id": "log_1705317540_234567",
      "type": "message_deleted",
      "date": "2024-01-15 09:19:00",
      "date_unixtime": "1705317540",
      "action": "deleted",
      "action_date": "2024-01-15 09:19:30",
      "action_date_unixtime": "1705317570",
      "chat": {
        "id": "123456789",
        "name": "Друзья",
        "type": "group_chat"
      },
      "from": {
        "id": "111333555",
        "name": "Максим"
      },
      "text": "[Message was deleted]",
      "deleted_text": "Кто идет в кино сегодня вечером?",
      "was_deleted": true,
      "out": false
    }
  ]
}
```

## Отслеживание изменений сообщений

### 🔄 Редактирование сообщений
Когда пользователь редактирует сообщение:
- Сохраняется текст предыдущей версии (не самый первый, а именно предыдущий)
- Записывается новый текст
- Фиксируется время редактирования
- При повторном редактировании `original_text` обновляется на предыдущую версию
- В HTML добавляется визуальное выделение с показом предыдущей и новой версии
- В JSON добавляются поля `original_text`, `was_edited`, `action`

### 🗑️ Удаление сообщений
Когда пользователь удаляет сообщение:
- Сохраняется полная информация об удаленном сообщении (текст, отправитель, время)
- Фиксируется время удаления
- В HTML отображается содержимое удаленного сообщения в специальном блоке
- В JSON добавляются поля `deleted_text`, `was_deleted`, `action`

### 🔗 Связывание записей
Каждая запись в логах имеет уникальные идентификаторы для связывания:
- **`message_id`** - ID сообщения в Telegram (как у ботов)
- **`peer_id`** - ID чата/пользователя
- **`log_entry_id`** - уникальный ID записи в логе
- **`related_log_entry_id`** - ссылка на связанную запись (для редактирований/удалений)

Это позволяет:
- Найти все изменения конкретного сообщения
- Построить полную историю редактирований
- Связать удаленные сообщения с оригиналами
- Программно анализировать цепочки изменений

### 📊 Статистика изменений
Система позволяет отслеживать:
- Частоту редактирования сообщений
- Время между отправкой и редактированием
- Количество удаленных сообщений
- Паттерны поведения пользователей
- Полную историю изменений каждого сообщения

## 🔍 Интерактивные фильтры в HTML

HTML лог включает мощную систему фильтрации для удобного просмотра:

### Доступные фильтры:
1. **Фильтр по чатам** - выпадающий список всех чатов
2. **Фильтр по типам сообщений**:
   - Все типы
   - Обычные сообщения
   - Редактированные сообщения
   - Удаленные сообщения
   - Служебные сообщения
3. **Поиск по тексту** - поиск по содержимому сообщений
4. **Счетчик сообщений** - показывает количество найденных/общих сообщений

### Особенности:
- ✅ **Работа в реальном времени** - фильтры применяются мгновенно
- ✅ **Комбинирование фильтров** - можно использовать несколько фильтров одновременно
- ✅ **Автозаполнение чатов** - список чатов формируется автоматически
- ✅ **Сохранение состояния** - фильтры остаются активными при обновлении
- ✅ **Индикация результатов** - показывает "No messages match" при отсутствии результатов

### Примеры использования:
- Найти все сообщения из "Рабочего чата"
- Показать только редактированные сообщения
- Найти все сообщения со словом "встреча"
- Комбинировать: "Рабочий чат" + "редактированные" + поиск "время"

## Типы сообщений, которые логируются

### ✅ Логируются:
- Обычные текстовые сообщения от пользователей
- Сообщения в личных чатах
- Сообщения в групповых чатах
- Сообщения в каналах
- Сообщения с эмодзи и специальными символами
- Служебные сообщения (присоединение к группе, смена названия и т.д.)
- Медиафайлы (фото, видео, аудио, документы)
- **Редактированные сообщения с оригинальным текстом**
- **Удаленные сообщения с временными метками**

### ❌ НЕ логируются:
- Локальные сообщения (черновики, отложенные сообщения)
- Исходящие сообщения (только входящие)

## Обработка специальных символов

Специальные символы экранируются для корректного отображения в одной строке:

```
Исходное сообщение:
Привет!
Как дела?
	Отлично!

В логе:
[2024.01.15 10:00:00] [Chat: Друг] [From: Петя] [ID: 12345] Message: Привет!\nКак дела?\n\tОтлично!
```

## Длинные сообщения

Сообщения длиннее 1000 символов обрезаются:

```
[2024.01.15 10:05:00] [Chat: Блогер] [From: Автор] [ID: 12346] Message: Очень длинное сообщение с множеством текста, которое превышает лимит в 1000 символов и поэтому будет обрезано в конце для экономии места в файле логирования и предотвращения создания слишком больших записей...
```

## Расположение файла

Файл `messages.txt` создается в рабочей директории Telegram Desktop:

### Windows:
```
%APPDATA%\Telegram Desktop\messages.txt
```

### Linux:
```
~/.local/share/TelegramDesktop/messages.txt
```

### macOS:
```
~/Library/Application Support/Telegram Desktop/messages.txt
```

## Мониторинг в реальном времени

Вы можете следить за новыми сообщениями в реальном времени:

### Linux/macOS:
```bash
tail -f ~/.local/share/TelegramDesktop/messages.txt
```

### Windows (PowerShell):
```powershell
Get-Content "$env:APPDATA\Telegram Desktop\messages.txt" -Wait -Tail 10
```

## Анализ логов

Примеры команд для анализа логов:

### Поиск сообщений от конкретного пользователя:
```bash
grep "From: Иван" messages.txt
```

### Подсчет сообщений за день:
```bash
grep "2024.01.15" messages.txt | wc -l
```

### Поиск сообщений в конкретном чате:
```bash
grep "Chat: Рабочий чат" messages.txt
```

## Безопасность и конфиденциальность

⚠️ **Важно**: Файл логирования содержит все ваши входящие сообщения в открытом виде. Убедитесь, что:

1. Файл защищен соответствующими правами доступа
2. Регулярно очищайте или архивируйте старые логи
3. Не передавайте файл логирования третьим лицам
4. Рассмотрите возможность шифрования файла для дополнительной безопасности

## Управление размером файла

Для предотвращения роста файла до больших размеров, рекомендуется:

1. Регулярно архивировать старые логи
2. Настроить ротацию логов (можно добавить в будущих версиях)
3. Мониторить размер файла

Пример скрипта для архивирования (Linux/macOS):
```bash
#!/bin/bash
cd ~/.local/share/TelegramDesktop/
if [ -f messages.txt ]; then
    mv messages.txt "messages_$(date +%Y%m%d_%H%M%S).txt"
    gzip "messages_$(date +%Y%m%d_%H%M%S).txt"
fi
```

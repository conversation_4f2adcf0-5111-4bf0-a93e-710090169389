# Схема tg://search для Telegram Desktop

## Описание

Добавлена новая URL схема `tg://search` для прямого перехода к поиску конкретного текста в определенном чате.

## Формат

```
tg://search?domain=USERNAME&text=SEARCH_TEXT
```

## Параметры

- **domain** (обязательный) - username пользователя, группы или канала (без символа @)
- **text** (обязательный) - текст для поиска в указанном чате

## Примеры использования

```
tg://search?domain=durov&text=hello
tg://search?domain=telegram&text=update
tg://search?domain=mychannel&text=важная информация
```

## Как это работает

1. При переходе по ссылке `tg://search?domain=username&text=query`:
   - Telegram Desktop ищет пользователя/чат по указанному username
   - Если пользователь найден локально, сразу выполняется поиск
   - Если пользователь не найден локально, выполняется запрос к серверу Telegram
   - После получения информации о пользователе открывается поиск по указанному тексту в этом чате

2. Поиск выполняется с использованием стандартной функциональности поиска Telegram Desktop
3. Результаты отображаются в обычном интерфейсе поиска

## Технические детали

### Файлы, которые были изменены:

1. **Telegram\SourceFiles\core\local_url_handlers.cpp**:
   - Добавлена функция `ResolveSearch()` для обработки новой схемы
   - Добавлен обработчик в массив `LocalUrlHandlers()`

### Интеграция с существующим кодом:

- Использует существующую функцию `resolveUsername()` для поиска пользователей
- Использует существующую функцию `searchMessages()` для выполнения поиска
- Полностью совместима с существующей архитектурой URL обработчиков

## Ограничения

1. Требует, чтобы username был публичным (доступным для поиска)
2. Поиск выполняется только в указанном чате, не глобально
3. Работает только с username, не поддерживает поиск по ID чата

## Безопасность

- Все параметры проходят валидацию
- Используются существующие механизмы авторизации и доступа
- Не позволяет обходить ограничения доступа к чатам

## Совместимость

- Совместима со всеми существующими tg:// схемами
- Не влияет на работу других функций Telegram Desktop
- Работает на всех поддерживаемых платформах (Windows, macOS, Linux)

# Схема tg://search для Telegram Desktop

## Описание

Добавлена новая URL схема `tg://search` для прямого перехода к поиску конкретного текста в определенном чате. Поддерживает как публичные, так и приватные чаты.

## Формат

```
tg://search?[CHAT_IDENTIFIER]&text=SEARCH_TEXT
```

## Параметры

### Обязательный параметр:
- **text** - текст для поиска

### Параметры идентификации чата (опциональные):
- **domain** - username пользователя, группы или канала (без символа @) для публичных чатов
- **channel** - числовой ID канала для приватных каналов
- **chat** - числовой ID группы для приватных групп
- **phone** - номер телефона пользователя (с + или без)

**Примечание:** Если не указан ни один параметр идентификации чата, выполняется глобальный поиск по всем вашим чатам.

**Как получить ID чатов:** См. файл [HOW_TO_GET_CHAT_IDS.md](HOW_TO_GET_CHAT_IDS.md) для подробных инструкций.

## Примеры использования

### Публичные чаты (по username):
```
tg://search?domain=durov&text=hello
tg://search?domain=telegram&text=update
tg://search?domain=mychannel&text=важная информация
```

### Приватные каналы (по channel ID):
```
tg://search?channel=1234567890&text=секретная информация
tg://search?channel=1001234567890&text=внутренние новости
```

### Приватные группы (по chat ID):
```
tg://search?chat=123456789&text=обсуждение проекта
tg://search?chat=987654321&text=планы на завтра
```

### Личные чаты (по номеру телефона):
```
tg://search?phone=+1234567890&text=встреча
tg://search?phone=1234567890&text=документы
```

### Глобальный поиск (по всем чатам):
```
tg://search?text=важная информация
tg://search?text=встреча завтра
tg://search?text=документы проекта
```

## Как это работает

1. При переходе по ссылке `tg://search?domain=username&text=query`:
   - Telegram Desktop ищет пользователя/чат по указанному username
   - Если пользователь найден локально, сразу выполняется поиск
   - Если пользователь не найден локально, выполняется запрос к серверу Telegram
   - После получения информации о пользователе открывается поиск по указанному тексту в этом чате

2. Поиск выполняется с использованием стандартной функциональности поиска Telegram Desktop
3. Результаты отображаются в обычном интерфейсе поиска

## Технические детали

### Файлы, которые были изменены:

1. **Telegram\SourceFiles\core\local_url_handlers.cpp**:
   - Добавлена функция `ResolveSearch()` для обработки новой схемы
   - Добавлен обработчик в массив `LocalUrlHandlers()`

### Интеграция с существующим кодом:

- Использует существующую функцию `resolveUsername()` для поиска пользователей
- Использует существующую функцию `searchMessages()` для выполнения поиска
- Полностью совместима с существующей архитектурой URL обработчиков

## Ограничения

1. Требует, чтобы username был публичным (доступным для поиска)
2. Поиск выполняется только в указанном чате, не глобально
3. Работает только с username, не поддерживает поиск по ID чата

## Безопасность

- Все параметры проходят валидацию
- Используются существующие механизмы авторизации и доступа
- Не позволяет обходить ограничения доступа к чатам

## Совместимость

- Совместима со всеми существующими tg:// схемами
- Не влияет на работу других функций Telegram Desktop
- Работает на всех поддерживаемых платформах (Windows, macOS, Linux)

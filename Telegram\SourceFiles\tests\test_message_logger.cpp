/*
This file is part of Telegram Desktop,
the official desktop application for the Telegram messaging service.

For license and copyright information please follow this link:
https://github.com/telegramdesktop/tdesktop/blob/master/LEGAL
*/
#include "tests/test_message_logger.h"

#include "data/data_message_logger.h"
#include "logs.h"

#include <QTest>
#include <QString>

namespace Tests {

void TestMessageLogger::testEscapeText() {
	// Test basic text
	QCOMPARE(Data::MessageLogger::escapeText("Hello World"), "Hello World");

	// Test HTML escaping
	QCOMPARE(Data::MessageLogger::escapeText("Hello & <World>"), "Hello &amp; &lt;World&gt;");

	// Test quotes
	QCOMPARE(Data::MessageLogger::escapeText("Say \"Hello\""), "Say &quot;Hello&quot;");

	// Test apostrophes
	QCOMPARE(Data::MessageLogger::escapeText("Don't"), "Don&#39;t");

	// Test long text truncation
	QString longText(2500, 'A');
	QString escaped = Data::MessageLogger::escapeText(longText);
	QVERIFY(escaped.length() <= 2000);
	QVERIFY(escaped.endsWith("..."));
}

void TestMessageLogger::testLogMacro() {
	// Test that MESSAGE_LOG macro compiles and works
	MESSAGE_LOG(("Test message: %1").arg("Hello"));

	// This should create a log entry in messages.txt file
	QVERIFY(true); // If we reach here, the macro compiled successfully
}

void TestMessageLogger::testLoggedMessageStructure() {
	// Test LoggedMessage structure
	Data::LoggedMessage message;
	message.id = 12345;
	message.chatName = "Test Chat";
	message.chatType = "private_chat";
	message.fromName = "Test User";
	message.text = "Hello, World!";
	message.originalText = "Original text";
	message.deletedText = "Deleted text content";
	message.mediaType = "photo";
	message.isService = false;
	message.isOutgoing = true;
	message.isDeleted = false;
	message.isEdited = true;
	message.editReason = "edited";
	message.actionDate = 1705317025;
	message.messageId = 12345;
	message.peerId = 987654321;
	message.logEntryId = "log_1705317025_123456";
	message.relatedLogEntryId = "log_1705317000_654321";

	// Verify structure is properly initialized
	QCOMPARE(message.id, 12345);
	QCOMPARE(message.chatName, QString("Test Chat"));
	QCOMPARE(message.chatType, QString("private_chat"));
	QCOMPARE(message.fromName, QString("Test User"));
	QCOMPARE(message.text, QString("Hello, World!"));
	QCOMPARE(message.originalText, QString("Original text"));
	QCOMPARE(message.deletedText, QString("Deleted text content"));
	QCOMPARE(message.mediaType, QString("photo"));
	QCOMPARE(message.isService, false);
	QCOMPARE(message.isOutgoing, true);
	QCOMPARE(message.isDeleted, false);
	QCOMPARE(message.isEdited, true);
	QCOMPARE(message.editReason, QString("edited"));
	QCOMPARE(message.actionDate, TimeId(1705317025));
	QCOMPARE(message.messageId, 12345);
	QCOMPARE(message.peerId, int64(987654321));
	QCOMPARE(message.logEntryId, QString("log_1705317025_123456"));
	QCOMPARE(message.relatedLogEntryId, QString("log_1705317000_654321"));
}

} // namespace Tests

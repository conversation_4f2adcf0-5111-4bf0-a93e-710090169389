/*
This file is part of Telegram Desktop,
the official desktop application for the Telegram messaging service.

For license and copyright information please follow this link:
https://github.com/telegramdesktop/tdesktop/blob/master/LEGAL
*/
#pragma once

#include "base/basic_types.h"

#include <QJsonObject>
#include <QJsonDocument>
#include <QFile>
#include <QTextStream>

class HistoryItem;

namespace Main {
class Session;
} // namespace Main

namespace Data {

struct LoggedMessage {
	int32 id = 0;
	TimeId date = 0;
	TimeId edited = 0;
	QString chatName;
	QString chatType;
	int64 chatId = 0;
	QString fromName;
	int64 fromId = 0;
	QString text;
	QString originalText; // For tracking edits (previous version)
	QString deletedText; // For deleted messages (what was deleted)
	QString mediaType;
	QString fileName;
	bool isService = false;
	bool isOutgoing = false;
	bool isDeleted = false;
	bool isEdited = false;
	QString editReason; // "edited" or "deleted"
	TimeId actionDate = 0; // When the edit/delete happened

	// Message linking for tracking changes
	int32 messageId = 0; // Telegram message ID for linking
	int64 peerId = 0; // Peer ID for full message identification
	QString logEntryId; // Unique log entry ID for this specific log record
	QString relatedLogEntryId; // ID of related log entry (original message for edits/deletes)

	// Reply/quote information
	int32 replyToMessageId = 0; // ID of the message this is replying to
	int64 replyToPeerId = 0; // Peer ID of the replied message (for cross-chat replies)
	QString replyToText; // Text of the quoted message (for reference)
};

enum class MessageAction {
	Added,
	Edited,
	Deleted
};

class MessageLogger final {
public:
	static void logMessage(not_null<HistoryItem*> item);
	static void logMessageEdited(not_null<HistoryItem*> item, const QString &originalText);
	static void logMessageDeleted(not_null<HistoryItem*> item);

	// New methods for logging messages not in memory (from API updates)
	static void logMessageEditedFromUpdate(const MTPMessage &message, PeerId peerId, not_null<Main::Session*> session);
	static void logMessageDeletedFromUpdate(const QVector<MTPint> &messageIds, PeerId peerId, not_null<Main::Session*> session);

	// Debug method
	static void logDebugMessage(const QString &debugText);
	static QString escapeText(const QString &text);

	// Initialize tracking
	static void initializeTracking();
	static void shutdownTracking();

	// Public access to tracking data for Session integration
	static QString getOriginalText(FullMsgId fullId);
	static void updateTrackedText(FullMsgId fullId, const QString &newText);
	static QString getOriginalLogEntryId(FullMsgId fullId);
	static QMutex& getTrackingMutex();

	// Message history access
	static QList<LoggedMessage> getMessageHistory(FullMsgId fullId);
	static bool hasMessageHistory(FullMsgId fullId);

private:
	static LoggedMessage convertToLoggedMessage(not_null<HistoryItem*> item, MessageAction action = MessageAction::Added);
	static void writeHtmlLog(const LoggedMessage &message, MessageAction action);
	static void writeJsonLog(const LoggedMessage &message, MessageAction action);
	static void initializeHtmlFile();
	static void initializeJsonFile();
	static QString getLogDirectory();
	static QString formatDateTime(TimeId timestamp);
	static QString formatHtmlMessage(const LoggedMessage &message, MessageAction action);
	static QJsonObject formatJsonMessage(const LoggedMessage &message, MessageAction action);

	// Message tracking
	static void trackMessage(not_null<HistoryItem*> item);
	static void updateTrackedMessage(not_null<HistoryItem*> item, const QString &originalText);
	static void removeTrackedMessage(not_null<HistoryItem*> item);

	static bool _htmlInitialized;
	static bool _jsonInitialized;
	static bool _trackingInitialized;

	// Storage for original message content to detect edits
	static QMap<FullMsgId, QString> _originalTexts;
	static QMap<FullMsgId, QString> _logEntryIds; // Map message ID to log entry ID
	static QMutex _trackingMutex;

	// Helper methods
	static QString generateLogEntryId();
	static QString formatFullMsgId(FullMsgId fullId);

	// Helper methods for API update logging
	static LoggedMessage convertFromMTPMessage(const MTPMessage &message, PeerId peerId, MessageAction action, not_null<Main::Session*> session);
	static LoggedMessage createDeletedMessageFromId(MsgId messageId, PeerId peerId, not_null<Main::Session*> session);
	static QString getChatName(PeerId peerId, not_null<Main::Session*> session);
	static QString getChatType(PeerId peerId, not_null<Main::Session*> session);
};

} // namespace Data

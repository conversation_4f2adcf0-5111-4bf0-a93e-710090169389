# Исправление логирования реакций в Telegram Desktop

## Проблема
Ранее система логирования сообщений в Telegram Desktop неправильно обрабатывала реакции на сообщения. Любая реакция (смайлик) на сообщение расценивалась как редактирование сообщения и отображалась в логах как изменение текста.

## Решение
Добавлена функция `isRealMessageEdit()` в класс `Session`, которая различает:

1. **Реальные редактирования** - когда изменился текст, медиа или другие важные части сообщения
2. **Изменения реакций** - когда изменились только реакции на сообщение

### Изменения в коде

#### 1. Файл `data_session.h`
- Добавлено объявление функции `isRealMessageEdit(not_null<HistoryItem*> item) const`

#### 2. Файл `data_session.cpp`
- Реализована функция `isRealMessageEdit()` которая:
  - Сравнивает текущий текст сообщения с ранее сохраненным
  - Возвращает `true` только если текст действительно изменился
  - Возвращает `false` если изменились только реакции/просмотры

- Модифицирована функция `setupMessageTrackingViewer()`:
  - Добавлена проверка `isRealMessageEdit(item)` перед логированием
  - Реакции больше не логируются как редактирования

### Логика работы

```cpp
bool Session::isRealMessageEdit(not_null<HistoryItem*> item) const {
    // Получаем предыдущий текст из системы отслеживания
    const auto fullId = item->fullId();
    const auto previousText = MessageLogger::getOriginalText(fullId);
    
    // Если нет предыдущего текста, считаем это реальным редактированием
    if (previousText.isEmpty()) {
        return true;
    }
    
    // Получаем текущий текст
    const auto currentText = item->isService()
        ? item->notificationText().text
        : item->originalText().text;
    
    // Если текст изменился - это реальное редактирование
    if (currentText != previousText) {
        return true;
    }
    
    // Если текст не изменился - это только реакции/просмотры
    return false;
}
```

## Техническая информация

### Как работает различение реакций от редактирований

В Telegram Desktop есть два разных типа обновлений:

1. **`updateMessageReactions`** - обрабатывается в `api_updates.cpp:1765-1786`
   - Вызывает `item->updateReactions()` напрямую
   - НЕ генерирует `MessageUpdate::Flag::Edited`
   - Используется только для изменения реакций

2. **`updateEditMessage`** - обрабатывается в `api_updates.cpp:1417-1420`
   - Вызывает `updateEditedMessage()` → `applyEdition()` → `finishEdition()` → `updateDependentMessages()`
   - Генерирует `MessageUpdate::Flag::Edited`
   - Используется для реальных редактирований сообщений

### Почему наше решение работает

Наша функция `isRealMessageEdit()` проверяет, изменился ли текст сообщения. Это работает, потому что:
- При реакциях текст остается неизменным
- При реальных редактированиях текст изменяется
- Мы сравниваем текущий текст с ранее сохраненным

## Результат
Теперь в логах будут отображаться только реальные редактирования сообщений. Реакции (смайлики) больше не будут ошибочно логироваться как изменения текста сообщений.

## Тестирование
Для проверки работы:
1. Включите логирование сообщений в настройках
2. Отправьте сообщение
3. Поставьте реакцию на сообщение - она НЕ должна появиться в логах как редактирование
4. Отредактируйте текст сообщения - это ДОЛЖНО появиться в логах как редактирование

## Совместимость
Изменения обратно совместимы и не влияют на существующую функциональность логирования.

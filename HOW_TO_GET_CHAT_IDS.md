# Как получить ID чатов для схемы tg2://search

Для использования схемы `tg2://search` с приватными чатами вам нужно знать их ID. Вот несколько способов получить эти ID:

## 1. Для приватных каналов и супергрупп (Channel ID)

### Способ 1: Через веб-версию Telegram
1. Откройте https://web.telegram.org
2. Перейдите в нужный канал/супергруппу
3. Посмотрите на URL в адресной строке: `https://web.telegram.org/k/#-1001234567890`
4. Число после `-100` это Channel ID: `1234567890`

### Способ 2: Через Telegram Desktop
1. Откройте канал/супергруппу в Telegram Desktop
2. Нажмите правой кнопкой мыши на любое сообщение
3. Выберите "Копировать ссылку на сообщение"
4. Ссылка будет выглядеть как: `https://t.me/c/1234567890/123`
5. Число между `/c/` и следующим `/` это Channel ID: `1234567890`

### Способ 3: Через боты
1. Добавьте бота @userinfobot в канал/супергруппу
2. Бот покажет ID чата

## 2. Для приватных групп (Chat ID)

### Способ 1: Через веб-версию Telegram
1. Откройте https://web.telegram.org
2. Перейдите в нужную группу
3. Посмотрите на URL: `https://web.telegram.org/k/#-123456789`
4. Число после `-` (без `-100`) это Chat ID: `123456789`

### Способ 2: Через боты
1. Добавьте бота @userinfobot в группу
2. Бот покажет ID группы

## 3. Для личных чатов

### По номеру телефона
Используйте номер телефона пользователя в международном формате:
- С плюсом: `+1234567890`
- Без плюса: `1234567890`

### По User ID (если известен)
1. Добавьте бота @userinfobot
2. Перешлите ему сообщение от нужного пользователя
3. Бот покажет User ID

## Примеры использования

### Приватный канал:
```
tg2://search?channel=1234567890&text=важные новости
```

### Приватная группа:
```
tg2://search?chat=123456789&text=обсуждение
```

### Личный чат:
```
tg2://search?phone=+1234567890&text=встреча
```

### Глобальный поиск:
```
tg2://search?text=документы
```

## Важные замечания

1. **Channel ID vs Chat ID**: 
   - Супергруппы и каналы используют Channel ID (обычно длинные числа)
   - Обычные группы используют Chat ID (обычно короткие числа)

2. **Безопасность**: 
   - ID чатов не являются секретной информацией
   - Но доступ к чату по-прежнему требует соответствующих разрешений

3. **Совместимость**:
   - Схема работает только с чатами, к которым у вас есть доступ
   - Для заблокированных или покинутых чатов поиск не будет работать

## Альтернативные способы

Если получение ID кажется сложным, вы всегда можете:
1. Использовать публичные чаты по username: `tg://search?domain=username&text=query`
2. Использовать глобальный поиск: `tg://search?text=query`

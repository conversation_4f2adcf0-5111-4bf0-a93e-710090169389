/*
This file is part of Telegram Desktop,
the official desktop application for the Telegram messaging service.

For license and copyright information please follow this link:
https://github.com/telegramdesktop/tdesktop/blob/master/LEGAL
*/
#include "boxes/message_history_box.h"

#include "lang/lang_keys.h"
#include "ui/widgets/labels.h"
#include "ui/widgets/box_content_divider.h"
#include "ui/wrap/vertical_layout.h"
#include "ui/text/text_utilities.h"
#include "ui/vertical_list.h"
#include "styles/style_boxes.h"
#include "styles/style_layers.h"
#include "styles/style_info.h"
#include "window/window_session_controller.h"
#include "data/data_session.h"
#include "data/data_message_logger.h"

#include <QtWidgets/QVBoxLayout>

MessageHistoryBox::MessageHistoryBox(
	QWidget*,
	not_null<Window::SessionController*> controller,
	FullMsgId messageId)
: _controller(controller)
, _messageId(messageId) {
}

void MessageHistoryBox::prepare() {
	setTitle(tr::lng_message_history_title());

	_history = Data::MessageLogger::getMessageHistory(_messageId);

	if (_history.isEmpty()) {
		setTitle(tr::lng_message_history_empty_title());
		addButton(tr::lng_close(), [=] { closeBox(); });

		auto content = object_ptr<Ui::VerticalLayout>(this);
		auto label = content.data()->add(
			object_ptr<Ui::FlatLabel>(
				content.data(),
				tr::lng_message_history_empty_text(),
				st::boxLabel),
			st::boxPadding);
		label->setTextColorOverride(st::windowSubTextFg->c);

		setInnerWidget(std::move(content));

		setDimensions(st::boxWidth, st::boxPadding.top() + label->height() + st::boxPadding.bottom());
		return;
	}

	setupContent();
	
	addButton(tr::lng_close(), [=] { closeBox(); });
	
	setDimensions(st::boxWideWidth, st::boxMaxListHeight);
}

void MessageHistoryBox::setInnerFocus() {
	if (_content) {
		_content->setFocus();
	}
}

void MessageHistoryBox::setupContent() {
	auto content = object_ptr<Ui::VerticalLayout>(this);
	_content = content.data();

	// Add header
	auto header = _content->add(
		object_ptr<Ui::FlatLabel>(
			_content,
			tr::lng_message_history_header(),
			st::boxLabel),
		st::boxPadding);
	header->setTextColorOverride(st::windowSubTextFg->c);

	// Add separator after header
	Ui::AddDivider(_content);

	// Add history entries
	for (const auto &entry : _history) {
		addHistoryEntry(entry);
	}

	// Force layout update
	_content->resizeToWidth(st::boxWidth);

	setInnerWidget(std::move(content), st::boxScroll);
}

void MessageHistoryBox::addHistoryEntry(const Data::LoggedMessage &entry) {
	auto container = _content->add(object_ptr<Ui::VerticalLayout>(_content));

	// Entry header with timestamp and action
	QString headerText;
	const auto timestamp = entry.actionDate > 0 ? entry.actionDate : entry.date;
	const auto dateTime = formatDateTime(timestamp);

	if (entry.isDeleted) {
		headerText = tr::lng_message_history_deleted(tr::now, lt_date, dateTime);
	} else if (entry.isEdited) {
		headerText = tr::lng_message_history_edited(tr::now, lt_date, dateTime);
	} else {
		headerText = tr::lng_message_history_original(tr::now, lt_date, dateTime);
	}
	
	auto header = container->add(
		object_ptr<Ui::FlatLabel>(
			container,
			headerText,
			st::defaultFlatLabel),
		st::boxRowPadding);
	header->setTextColorOverride(st::windowActiveTextFg->c);

	// Entry content
	const auto contentText = formatEntryText(entry);

	if (!contentText.isEmpty()) {
		auto content = container->add(
			object_ptr<Ui::FlatLabel>(
				container,
				contentText,
				st::boxLabel),
			QMargins(st::boxPadding.left() + st::boxPadding.left(), 0, st::boxPadding.right(), 0));

		// Set different colors for different entry types
		if (entry.isDeleted) {
			content->setTextColorOverride(st::boxTextFgError->c);
		} else if (entry.isEdited) {
			content->setTextColorOverride(st::windowSubTextFg->c);
		}
	}

	// Add some spacing between entries
	if (&entry != &_history.last()) {
		Ui::AddSkip(container, st::boxPadding.top());
	}
}

QString MessageHistoryBox::formatEntryText(const Data::LoggedMessage &entry) {
	if (entry.isDeleted) {
		return entry.deletedText.isEmpty() ? entry.text : entry.deletedText;
	} else if (entry.isEdited) {
		QString result;
		if (!entry.originalText.isEmpty()) {
			result += tr::lng_message_history_previous_text(tr::now) + "\n" + entry.originalText + "\n\n";
		}
		result += tr::lng_message_history_current_text(tr::now) + "\n" + entry.text;
		return result;
	} else {
		return entry.text;
	}
}

QString MessageHistoryBox::formatDateTime(TimeId timestamp) {
	const auto dateTime = QDateTime::fromSecsSinceEpoch(timestamp);
	return dateTime.toString("dd.MM.yyyy hh:mm:ss");
}

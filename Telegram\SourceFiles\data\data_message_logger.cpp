/*
This file is part of Telegram Desktop,
the official desktop application for the Telegram messaging service.

For license and copyright information please follow this link:
https://github.com/telegramdesktop/tdesktop/blob/master/LEGAL
*/
#include "data/data_message_logger.h"

#include "history/history_item.h"
#include "history/history.h"
#include "data/data_peer.h"
#include "data/data_user.h"
#include "data/data_chat.h"
#include "data/data_channel.h"
#include "data/data_media_types.h"
#include "data/data_document.h"
#include "data/data_photo.h"
#include "data/data_session.h"
#include "core/file_utilities.h"
#include "logs.h"
#include "mtproto/mtproto_response.h"
#include "main/main_session.h"
#include "core/application.h"
#include "core/core_settings.h"

#include <QDateTime>
#include <QDir>
#include <QStandardPaths>
#include <QJsonArray>
#include <QMutex>
#include <QRandomGenerator>

namespace Data {

// Static member definitions
bool MessageLogger::_htmlInitialized = false;
bool MessageLogger::_jsonInitialized = false;
bool MessageLogger::_trackingInitialized = false;
QMap<FullMsgId, QString> MessageLogger::_originalTexts;
QMap<FullMsgId, QString> MessageLogger::_logEntryIds;
QMutex MessageLogger::_trackingMutex;

// Thread-safe mutex for file operations
static QMutex logMutex;

void MessageLogger::logMessage(not_null<HistoryItem*> item) {
	// Safety checks
	if (!item) {
		return;
	}

	// Check if tracking is initialized
	if (!_trackingInitialized) {
		return;
	}

	try {
		const auto message = convertToLoggedMessage(item, MessageAction::Added);

		// Write to both HTML and JSON formats
		writeHtmlLog(message, MessageAction::Added);
		writeJsonLog(message, MessageAction::Added);

		// Track this message for future edits/deletes
		trackMessage(item);
	} catch (...) {
		// Silently ignore errors to prevent crashes
	}
}

void MessageLogger::logMessageEdited(not_null<HistoryItem*> item, const QString &originalText) {
	// Safety checks
	if (!item) {
		return;
	}

	// Check if tracking is initialized
	if (!_trackingInitialized) {
		return;
	}

	try {
		auto message = convertToLoggedMessage(item, MessageAction::Edited);
		message.originalText = originalText;
		message.isEdited = true;
		message.editReason = "edited";
		message.actionDate = QDateTime::currentSecsSinceEpoch();

		writeHtmlLog(message, MessageAction::Edited);
		writeJsonLog(message, MessageAction::Edited);

		// Update tracked message
		updateTrackedMessage(item, originalText);
	} catch (...) {
		// Silently ignore errors to prevent crashes
	}
}

void MessageLogger::logMessageDeleted(not_null<HistoryItem*> item) {
	// Safety checks
	if (!item) {
		return;
	}

	// Check if tracking is initialized
	if (!_trackingInitialized) {
		return;
	}

	try {
		auto message = convertToLoggedMessage(item, MessageAction::Deleted);
		message.isDeleted = true;
		message.editReason = "deleted";
		message.actionDate = QDateTime::currentSecsSinceEpoch();

		// Save the text that was deleted for reference
		message.deletedText = message.text;

		writeHtmlLog(message, MessageAction::Deleted);
		writeJsonLog(message, MessageAction::Deleted);

		// Remove from tracking
		removeTrackedMessage(item);
	} catch (...) {
		// Silently ignore errors to prevent crashes
	}
}

void MessageLogger::logMessageEditedFromUpdate(const MTPMessage &message, PeerId peerId, not_null<Main::Session*> session) {
	// Check if logging is enabled
	if (!Core::App().settings().messageLoggingEnabled()) {
		return;
	}

	// Check if tracking is initialized
	if (!_trackingInitialized) {
		LOG(("MessageLogger: Tracking not initialized, skipping edit log"));
		return;
	}

	try {
		LOG(("MessageLogger: Processing edit from API update for peer %1").arg(peerId.value));
		auto loggedMessage = convertFromMTPMessage(message, peerId, MessageAction::Edited, session);
		loggedMessage.isEdited = true;
		loggedMessage.editReason = "edited (from API update)";
		loggedMessage.actionDate = QDateTime::currentSecsSinceEpoch();

		// Try to get original text from tracking if available
		const auto fullId = FullMsgId(peerId, loggedMessage.messageId);
		QMutexLocker locker(&_trackingMutex);
		if (_originalTexts.contains(fullId)) {
			loggedMessage.originalText = _originalTexts[fullId];
			// Update tracking with new text
			_originalTexts[fullId] = loggedMessage.text;
		} else {
			// Message not in tracking, mark as unknown original
			loggedMessage.originalText = "[Original text unknown - message not in memory]";
		}

		writeHtmlLog(loggedMessage, MessageAction::Edited);
		writeJsonLog(loggedMessage, MessageAction::Edited);
		LOG(("MessageLogger: Successfully logged edit from API update for message %1").arg(loggedMessage.messageId));
	} catch (...) {
		LOG(("MessageLogger: Error logging edit from API update"));
		// Silently ignore errors to prevent crashes
	}
}

void MessageLogger::logMessageDeletedFromUpdate(const QVector<MTPint> &messageIds, PeerId peerId, not_null<Main::Session*> session) {
	// Check if logging is enabled
	if (!Core::App().settings().messageLoggingEnabled()) {
		return;
	}

	// Check if tracking is initialized
	if (!_trackingInitialized) {
		LOG(("MessageLogger: Tracking not initialized, skipping delete log"));
		return;
	}

	try {
		LOG(("MessageLogger: Processing %1 deletions from API update for peer %2").arg(messageIds.size()).arg(peerId.value));
		for (const auto &msgId : messageIds) {
			auto loggedMessage = createDeletedMessageFromId(msgId.v, peerId, session);
			loggedMessage.isDeleted = true;
			loggedMessage.editReason = "deleted (from API update)";
			loggedMessage.actionDate = QDateTime::currentSecsSinceEpoch();

			// Try to get original text from tracking if available
			const auto fullId = FullMsgId(peerId, msgId.v);
			QMutexLocker locker(&_trackingMutex);
			if (_originalTexts.contains(fullId)) {
				loggedMessage.deletedText = _originalTexts[fullId];
				loggedMessage.text = _originalTexts[fullId]; // For display purposes
				// Remove from tracking
				_originalTexts.remove(fullId);
				_logEntryIds.remove(fullId);
			} else {
				// Message not in tracking
				loggedMessage.deletedText = "[Deleted message content unknown - message not in memory]";
				loggedMessage.text = "[Deleted message content unknown - message not in memory]";
			}

			writeHtmlLog(loggedMessage, MessageAction::Deleted);
			writeJsonLog(loggedMessage, MessageAction::Deleted);
			LOG(("MessageLogger: Successfully logged deletion from API update for message %1").arg(msgId.v));
		}
	} catch (...) {
		LOG(("MessageLogger: Error logging deletions from API update"));
		// Silently ignore errors to prevent crashes
	}
}

LoggedMessage MessageLogger::convertToLoggedMessage(not_null<HistoryItem*> item, MessageAction action) {
	LoggedMessage message;

	// Safety check
	if (!item) {
		return message;
	}

	// Basic message info
	message.id = item->id.bare;
	message.date = item->date();
	message.edited = 0; // Edit date is not directly accessible, would need to track edits separately
	message.isService = item->isService();
	message.isOutgoing = item->out();

	// Message linking info
	const auto fullId = item->fullId();
	message.messageId = item->id.bare;
	message.peerId = fullId.peer.value;

	// For original messages, use existing ID if available, otherwise generate new
	if (action == MessageAction::Added) {
		QMutexLocker locker(&_trackingMutex);
		if (_logEntryIds.contains(fullId)) {
			message.logEntryId = _logEntryIds[fullId];
		} else {
			message.logEntryId = generateLogEntryId();
			_logEntryIds[fullId] = message.logEntryId;
		}
	} else {
		// For edits and deletes, generate new ID and link to original
		message.logEntryId = generateLogEntryId();
		message.relatedLogEntryId = getOriginalLogEntryId(fullId);
	}

	// Chat information
	const auto peer = item->history()->peer;
	message.chatId = peer->id.value;

	if (peer->isUser()) {
		message.chatType = "private_chat";
		message.chatName = peer->asUser()->name();
	} else if (peer->isChat()) {
		message.chatType = "group_chat";
		message.chatName = peer->asChat()->name();
	} else if (peer->isChannel()) {
		message.chatType = peer->asChannel()->isMegagroup() ? "supergroup" : "channel";
		message.chatName = peer->asChannel()->name();
	} else {
		message.chatType = "unknown";
		message.chatName = "Unknown";
	}

	// Sender information
	const auto from = item->from();
	if (from) {
		message.fromId = from->id.value;
		if (from->isUser()) {
			message.fromName = from->asUser()->name();
		} else if (from->isChat()) {
			message.fromName = from->asChat()->name();
		} else if (from->isChannel()) {
			message.fromName = from->asChannel()->name();
		} else {
			message.fromName = "Unknown";
		}
	}

	// Message text
	if (item->isService()) {
		const auto notificationText = item->notificationText();
		message.text = notificationText.text;
	} else {
		const auto originalText = item->originalText();
		message.text = originalText.text;
	}

	// Media information
	if (const auto media = item->media()) {
		if (const auto photo = media->photo()) {
			message.mediaType = "photo";
		} else if (const auto document = media->document()) {
			if (document->isVideoFile()) {
				message.mediaType = "video";
			} else if (document->isVoiceMessage()) {
				message.mediaType = "voice_note";
			} else if (document->isVideoMessage()) {
				message.mediaType = "video_note";
			} else if (document->isAudioFile()) {
				message.mediaType = "audio";
			} else if (document->isAnimation()) {
				message.mediaType = "animation";
			} else if (document->sticker()) {
				message.mediaType = "sticker";
			} else {
				message.mediaType = "document";
			}
			message.fileName = document->filename();
		}
	}

	// Reply/quote information
	const auto replyToFullId = item->replyToFullId();
	if (replyToFullId.msg) {
		message.replyToMessageId = replyToFullId.msg.bare;
		message.replyToPeerId = replyToFullId.peer.value;

		// Try to get the text of the quoted message
		if (const auto replyToItem = item->history()->owner().message(replyToFullId)) {
			const auto replyText = replyToItem->originalText().text;
			// Limit quote text to first 100 characters for readability
			if (replyText.length() > 100) {
				message.replyToText = replyText.left(100) + "...";
			} else {
				message.replyToText = replyText;
			}
		}
	}

	return message;
}

void MessageLogger::writeHtmlLog(const LoggedMessage &message, MessageAction action) {
	QMutexLocker locker(&logMutex);

	if (!_htmlInitialized) {
		initializeHtmlFile();
		_htmlInitialized = true;
	}

	const auto logDir = getLogDirectory();
	const auto htmlPath = logDir + "/messages.html";

	// Read existing content
	QFile file(htmlPath);
	QString content;
	if (file.open(QIODevice::ReadOnly)) {
		QTextStream readStream(&file);
		readStream.setCodec("UTF-8");
		content = readStream.readAll();
		file.close();
	}

	// Insert new message at the beginning of message history (newest first)
	const auto insertPos = content.indexOf("    <div class=\"history\" id=\"message_history\">\n");
	if (insertPos != -1) {
		const auto afterHistoryTag = insertPos + QString("    <div class=\"history\" id=\"message_history\">\n").length();
		content.insert(afterHistoryTag, formatHtmlMessage(message, action) + "\n");
	} else {
		// Fallback: try to find any existing message and insert before it
		const auto firstMessagePos = content.indexOf("      <div class=\"message");
		if (firstMessagePos != -1) {
			content.insert(firstMessagePos, formatHtmlMessage(message, action) + "\n");
		} else {
			// Last fallback: append to end (for empty log)
			const auto beforeClosingTags = content.lastIndexOf("    </div>\n  </div>\n</body>");
			if (beforeClosingTags != -1) {
				content.insert(beforeClosingTags, formatHtmlMessage(message, action) + "\n");
			} else {
				content += formatHtmlMessage(message, action) + "\n";
			}
		}
	}

	// Write back
	if (file.open(QIODevice::WriteOnly)) {
		QTextStream writeStream(&file);
		writeStream.setCodec("UTF-8");
		writeStream << content;
	}
}

void MessageLogger::writeJsonLog(const LoggedMessage &message, MessageAction action) {
	QMutexLocker locker(&logMutex);

	if (!_jsonInitialized) {
		initializeJsonFile();
		_jsonInitialized = true;
	}

	const auto logDir = getLogDirectory();
	const auto jsonPath = logDir + "/messages.json";

	// Read existing JSON
	QFile file(jsonPath);
	QJsonDocument doc;
	QJsonObject root;
	QJsonArray messages;

	if (file.exists() && file.open(QIODevice::ReadOnly)) {
		const auto data = file.readAll();
		file.close();

		doc = QJsonDocument::fromJson(data);
		if (doc.isObject()) {
			root = doc.object();
			if (root.contains("messages") && root["messages"].isArray()) {
				messages = root["messages"].toArray();
			}
		}
	}

	// Add new message at the beginning (newest first)
	messages.prepend(formatJsonMessage(message, action));
	root["messages"] = messages;

	// Write back to file
	if (file.open(QIODevice::WriteOnly)) {
		QTextStream stream(&file);
		stream.setCodec("UTF-8");
		doc.setObject(root);
		stream << doc.toJson(QJsonDocument::Indented);
	}
}

void MessageLogger::initializeHtmlFile() {
	const auto logDir = getLogDirectory();
	QDir().mkpath(logDir);

	const auto htmlPath = logDir + "/messages.html";
	QFile file(htmlPath);

	if (!file.exists() && file.open(QIODevice::WriteOnly)) {
		QTextStream stream(&file);
		stream.setCodec("UTF-8");

		stream << "<!DOCTYPE html>\n";
		stream << "<html>\n";
		stream << "<head>\n";
		stream << "  <meta charset=\"utf-8\">\n";
		stream << "  <title>Telegram Messages Log</title>\n";
		stream << "  <style>\n";
		stream << "    /* Messages are displayed in reverse chronological order (newest first) */\n";
		stream << "    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; background: #f5f5f5; }\n";
		stream << "    .page_body { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }\n";
		stream << "    .message { border-bottom: 1px solid #eee; padding: 15px 0; }\n";
		stream << "    .message:last-child { border-bottom: none; }\n";
		stream << "    .message_header { color: #666; font-size: 13px; margin-bottom: 5px; }\n";
		stream << "    .message_text { color: #333; line-height: 1.4; }\n";
		stream << "    .service_message { font-style: italic; color: #888; }\n";
		stream << "    .outgoing { background: #e3f2fd; border-radius: 8px; padding: 10px; }\n";
		stream << "    .media_info { color: #1976d2; font-size: 12px; }\n";
		stream << "    .edited_message { border-left: 3px solid #ff9800; padding-left: 10px; }\n";
		stream << "    .deleted_message { border-left: 3px solid #f44336; padding-left: 10px; opacity: 0.7; }\n";
		stream << "    .edit_info { color: #ff9800; font-weight: bold; font-size: 11px; }\n";
		stream << "    .delete_info { color: #f44336; font-weight: bold; font-size: 11px; }\n";
		stream << "    .original_text { background: #fff3e0; padding: 8px; border-radius: 4px; margin-bottom: 8px; }\n";
		stream << "    .edited_text { background: #e8f5e8; padding: 8px; border-radius: 4px; }\n";
		stream << "    .deleted_info { color: #f44336; font-weight: bold; margin-bottom: 5px; }\n";
		stream << "    .deleted_content { background: #ffebee; padding: 8px; border-radius: 4px; color: #666; }\n";
		stream << "    .related_info { color: #666; font-size: 10px; font-style: italic; }\n";
		stream << "    [data-log-entry-id] { position: relative; }\n";
		stream << "    .reply_quote { background: #f0f0f0; border-left: 3px solid #1976d2; padding: 8px; margin-bottom: 8px; border-radius: 4px; font-size: 13px; color: #555; }\n";
		stream << "    \n";
		stream << "    /* Filter Controls */\n";
		stream << "    .filter_controls { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #e9ecef; }\n";
		stream << "    .filter_group { display: inline-block; margin-right: 20px; margin-bottom: 10px; }\n";
		stream << "    .filter_group label { display: block; font-weight: bold; margin-bottom: 5px; color: #495057; font-size: 14px; }\n";
		stream << "    .filter_group select, .filter_group input { padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px; min-width: 150px; }\n";
		stream << "    .filter_group select:focus, .filter_group input:focus { outline: none; border-color: #1976d2; box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2); }\n";
		stream << "    .filter_stats { float: right; margin-top: 25px; color: #666; font-weight: bold; }\n";
		stream << "    .message.hidden { display: none; }\n";
		stream << "    .no_results { text-align: center; padding: 40px; color: #666; font-style: italic; }\n";
		stream << "  </style>\n";
		stream << "</head>\n";
		stream << "<body>\n";
		stream << "  <div class=\"page_body\">\n";
		stream << "    <h1>Telegram Messages Log</h1>\n";
		stream << "    <p style=\"color: #666; font-style: italic; margin-bottom: 20px;\">Messages are displayed in reverse chronological order (newest first)</p>\n";
		stream << "    \n";
		stream << "    <!-- Filter Controls -->\n";
		stream << "    <div class=\"filter_controls\">\n";
		stream << "      <div class=\"filter_group\">\n";
		stream << "        <label for=\"chat_filter\">Filter by Chat:</label>\n";
		stream << "        <select id=\"chat_filter\">\n";
		stream << "          <option value=\"\">All Chats</option>\n";
		stream << "        </select>\n";
		stream << "      </div>\n";
		stream << "      <div class=\"filter_group\">\n";
		stream << "        <label for=\"type_filter\">Filter by Type:</label>\n";
		stream << "        <select id=\"type_filter\">\n";
		stream << "          <option value=\"\">All Types</option>\n";
		stream << "          <option value=\"message\">Messages</option>\n";
		stream << "          <option value=\"edited_message\">Edited</option>\n";
		stream << "          <option value=\"deleted_message\">Deleted</option>\n";
		stream << "          <option value=\"service_message\">Service</option>\n";
		stream << "        </select>\n";
		stream << "      </div>\n";
		stream << "      <div class=\"filter_group\">\n";
		stream << "        <input type=\"text\" id=\"search_filter\" placeholder=\"Search in messages...\">\n";
		stream << "      </div>\n";
		stream << "      <div class=\"filter_stats\">\n";
		stream << "        <span id=\"message_count\">0 messages</span>\n";
		stream << "      </div>\n";
		stream << "    </div>\n";
		stream << "    \n";
		stream << "    <div class=\"history\" id=\"message_history\">\n";
		stream << "    </div>\n";
		stream << "  </div>\n";
		stream << "  \n";
		stream << "  <script>\n";
		stream << "    // Message filtering functionality\n";
		stream << "    let allMessages = [];\n";
		stream << "    let allChats = new Set();\n";
		stream << "    \n";
		stream << "    function initializeFilters() {\n";
		stream << "      // Collect all messages and chats\n";
		stream << "      const messages = document.querySelectorAll('.message');\n";
		stream << "      allMessages = Array.from(messages);\n";
		stream << "      \n";
		stream << "      allMessages.forEach(msg => {\n";
		stream << "        const chatName = msg.getAttribute('data-chat-name');\n";
		stream << "        if (chatName) allChats.add(chatName);\n";
		stream << "      });\n";
		stream << "      \n";
		stream << "      // Populate chat filter\n";
		stream << "      const chatFilter = document.getElementById('chat_filter');\n";
		stream << "      Array.from(allChats).sort().forEach(chat => {\n";
		stream << "        const option = document.createElement('option');\n";
		stream << "        option.value = chat;\n";
		stream << "        option.textContent = chat;\n";
		stream << "        chatFilter.appendChild(option);\n";
		stream << "      });\n";
		stream << "      \n";
		stream << "      updateMessageCount();\n";
		stream << "    }\n";
		stream << "    \n";
		stream << "    function filterMessages() {\n";
		stream << "      const chatFilter = document.getElementById('chat_filter').value;\n";
		stream << "      const typeFilter = document.getElementById('type_filter').value;\n";
		stream << "      const searchFilter = document.getElementById('search_filter').value.toLowerCase();\n";
		stream << "      \n";
		stream << "      let visibleCount = 0;\n";
		stream << "      \n";
		stream << "      allMessages.forEach(msg => {\n";
		stream << "        let visible = true;\n";
		stream << "        \n";
		stream << "        // Chat filter\n";
		stream << "        if (chatFilter && msg.getAttribute('data-chat-name') !== chatFilter) {\n";
		stream << "          visible = false;\n";
		stream << "        }\n";
		stream << "        \n";
		stream << "        // Type filter\n";
		stream << "        if (typeFilter) {\n";
		stream << "          const msgType = msg.getAttribute('data-message-type');\n";
		stream << "          if (msgType !== typeFilter) {\n";
		stream << "            visible = false;\n";
		stream << "          }\n";
		stream << "        }\n";
		stream << "        \n";
		stream << "        // Search filter\n";
		stream << "        if (searchFilter) {\n";
		stream << "          const text = msg.textContent.toLowerCase();\n";
		stream << "          if (!text.includes(searchFilter)) {\n";
		stream << "            visible = false;\n";
		stream << "          }\n";
		stream << "        }\n";
		stream << "        \n";
		stream << "        if (visible) {\n";
		stream << "          msg.classList.remove('hidden');\n";
		stream << "          visibleCount++;\n";
		stream << "        } else {\n";
		stream << "          msg.classList.add('hidden');\n";
		stream << "        }\n";
		stream << "      });\n";
		stream << "      \n";
		stream << "      updateMessageCount(visibleCount);\n";
		stream << "      showNoResultsMessage(visibleCount === 0);\n";
		stream << "    }\n";
		stream << "    \n";
		stream << "    function updateMessageCount(visible = null) {\n";
		stream << "      const total = allMessages.length;\n";
		stream << "      const count = visible !== null ? visible : total;\n";
		stream << "      const countText = visible !== null ? `${count} of ${total} messages` : `${total} messages`;\n";
		stream << "      document.getElementById('message_count').textContent = countText;\n";
		stream << "    }\n";
		stream << "    \n";
		stream << "    function showNoResultsMessage(show) {\n";
		stream << "      let noResultsDiv = document.getElementById('no_results');\n";
		stream << "      \n";
		stream << "      if (show && !noResultsDiv) {\n";
		stream << "        noResultsDiv = document.createElement('div');\n";
		stream << "        noResultsDiv.id = 'no_results';\n";
		stream << "        noResultsDiv.className = 'no_results';\n";
		stream << "        noResultsDiv.textContent = 'No messages match the current filters.';\n";
		stream << "        document.getElementById('message_history').appendChild(noResultsDiv);\n";
		stream << "      } else if (!show && noResultsDiv) {\n";
		stream << "        noResultsDiv.remove();\n";
		stream << "      }\n";
		stream << "    }\n";
		stream << "    \n";
		stream << "    // Event listeners\n";
		stream << "    document.addEventListener('DOMContentLoaded', function() {\n";
		stream << "      initializeFilters();\n";
		stream << "      \n";
		stream << "      document.getElementById('chat_filter').addEventListener('change', filterMessages);\n";
		stream << "      document.getElementById('type_filter').addEventListener('change', filterMessages);\n";
		stream << "      document.getElementById('search_filter').addEventListener('input', filterMessages);\n";
		stream << "    });\n";
		stream << "  </script>\n";
		stream << "</body>\n";
		stream << "</html>";
	}
}

void MessageLogger::initializeJsonFile() {
	const auto logDir = getLogDirectory();
	QDir().mkpath(logDir);

	const auto jsonPath = logDir + "/messages.json";
	QFile file(jsonPath);

	if (!file.exists() && file.open(QIODevice::WriteOnly)) {
		QTextStream stream(&file);
		stream.setCodec("UTF-8");

		QJsonObject root;
		root["about"] = "Telegram Desktop Messages Log";
		root["export_date"] = formatDateTime(QDateTime::currentSecsSinceEpoch());
		root["messages"] = QJsonArray();

		QJsonDocument doc(root);
		stream << doc.toJson(QJsonDocument::Indented);
	}
}

QString MessageLogger::getLogDirectory() {
	const auto documentsPath = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation);
	return documentsPath + "/Telegram Desktop/message_logs";
}

QString MessageLogger::formatDateTime(TimeId timestamp) {
	const auto dateTime = QDateTime::fromSecsSinceEpoch(timestamp);
	return dateTime.toString("yyyy-MM-dd hh:mm:ss");
}

QString MessageLogger::formatHtmlMessage(const LoggedMessage &message, MessageAction action) {
	QString html;
	QString messageClass = message.isService ? "message service_message" :
	                      (message.isOutgoing ? "message outgoing" : "message");

	// Add special classes for edited/deleted messages
	if (action == MessageAction::Edited) {
		messageClass += " edited_message";
	} else if (action == MessageAction::Deleted) {
		messageClass += " deleted_message";
	}

	// Determine message type for filtering
	QString messageType = "message";
	if (message.isService) {
		messageType = "service_message";
	} else if (action == MessageAction::Edited) {
		messageType = "edited_message";
	} else if (action == MessageAction::Deleted) {
		messageType = "deleted_message";
	}

	html += QString("      <div class=\"%1\" data-log-entry-id=\"%2\" data-chat-name=\"%3\" data-message-type=\"%4\">\n")
		.arg(messageClass)
		.arg(message.logEntryId)
		.arg(escapeText(message.chatName))
		.arg(messageType);
	html += QString("        <div class=\"message_header\">\n");
	html += QString("          <strong>%1</strong> (%2) - %3")
		.arg(escapeText(message.chatName))
		.arg(message.chatType)
		.arg(formatDateTime(message.date));

	// Add message ID info
	html += QString(" [ID: %1]").arg(message.messageId);

	// Add reply information
	if (message.replyToMessageId > 0) {
		html += QString(" [Reply to: %1").arg(message.replyToMessageId);
		if (message.replyToPeerId != message.peerId) {
			html += QString(" from chat %1").arg(message.replyToPeerId);
		}
		html += "]";
	}

	if (!message.fromName.isEmpty()) {
		html += QString(" - From: %1").arg(escapeText(message.fromName));
	}

	if (message.edited > 0) {
		html += QString(" (edited: %1)").arg(formatDateTime(message.edited));
	}

	// Add action information
	if (action == MessageAction::Edited) {
		html += QString(" <span class=\"edit_info\">[EDITED at %1]</span>").arg(formatDateTime(message.actionDate));
		if (!message.relatedLogEntryId.isEmpty()) {
			html += QString(" <span class=\"related_info\">(relates to: %1)</span>").arg(message.relatedLogEntryId);
		}
	} else if (action == MessageAction::Deleted) {
		html += QString(" <span class=\"delete_info\">[DELETED at %1]</span>").arg(formatDateTime(message.actionDate));
		if (!message.relatedLogEntryId.isEmpty()) {
			html += QString(" <span class=\"related_info\">(relates to: %1)</span>").arg(message.relatedLogEntryId);
		}
	}

	html += QString("\n        </div>\n");
	html += QString("        <div class=\"message_text\">\n");

	// Show quoted message if this is a reply
	if (message.replyToMessageId > 0 && !message.replyToText.isEmpty()) {
		html += QString("          <div class=\"reply_quote\"><strong>Replying to:</strong> %1</div>\n")
			.arg(escapeText(message.replyToText).replace("\n", "<br>"));
	}

	// Show original text for edited messages
	if (action == MessageAction::Edited && !message.originalText.isEmpty()) {
		html += QString("          <div class=\"original_text\"><strong>Original:</strong> %1</div>\n")
			.arg(escapeText(message.originalText).replace("\n", "<br>"));
		html += QString("          <div class=\"edited_text\"><strong>Edited to:</strong> ");
	}

	if (!message.mediaType.isEmpty()) {
		html += QString("          <div class=\"media_info\">[%1").arg(message.mediaType);
		if (!message.fileName.isEmpty()) {
			html += QString(": %1").arg(escapeText(message.fileName));
		}
		html += "]</div>\n";
	}

	if (!message.text.isEmpty()) {
		if (action == MessageAction::Deleted) {
			html += QString("          <div class=\"deleted_info\"><strong>Deleted message:</strong></div>\n");
			html += QString("          <div class=\"deleted_content\">%1</div>\n")
				.arg(escapeText(message.deletedText).replace("\n", "<br>"));
		} else {
			html += QString("          %1\n").arg(escapeText(message.text).replace("\n", "<br>"));
		}
	}

	// Close edited text div if needed
	if (action == MessageAction::Edited && !message.originalText.isEmpty()) {
		html += QString("</div>");
	}

	html += QString("        </div>\n");
	html += QString("      </div>");

	return html;
}

QJsonObject MessageLogger::formatJsonMessage(const LoggedMessage &message, MessageAction action) {
	QJsonObject obj;

	obj["id"] = QString::number(message.id);

	// Message linking information
	obj["message_id"] = QString::number(message.messageId);
	obj["peer_id"] = QString::number(message.peerId);
	obj["log_entry_id"] = message.logEntryId;
	if (!message.relatedLogEntryId.isEmpty()) {
		obj["related_log_entry_id"] = message.relatedLogEntryId;
	}

	// Set type based on action and service status
	QString type = message.isService ? "service" : "message";
	if (action == MessageAction::Edited) {
		type += "_edited";
	} else if (action == MessageAction::Deleted) {
		type += "_deleted";
	}
	obj["type"] = type;

	obj["date"] = formatDateTime(message.date);
	obj["date_unixtime"] = QString::number(message.date);

	// Add action information
	if (action != MessageAction::Added) {
		obj["action"] = (action == MessageAction::Edited) ? "edited" : "deleted";
		obj["action_date"] = formatDateTime(message.actionDate);
		obj["action_date_unixtime"] = QString::number(message.actionDate);
	}

	if (message.edited > 0) {
		obj["edited"] = formatDateTime(message.edited);
		obj["edited_unixtime"] = QString::number(message.edited);
	}

	// Chat information
	QJsonObject chat;
	chat["id"] = QString::number(message.chatId);
	chat["name"] = message.chatName;
	chat["type"] = message.chatType;
	obj["chat"] = chat;

	// Sender information
	if (!message.fromName.isEmpty()) {
		QJsonObject from;
		from["id"] = QString::number(message.fromId);
		from["name"] = message.fromName;
		obj["from"] = from;
	}

	// Message content
	if (!message.text.isEmpty()) {
		if (action == MessageAction::Deleted) {
			obj["text"] = "[Message was deleted]";
			obj["deleted_text"] = message.deletedText;
			obj["was_deleted"] = true;
		} else {
			obj["text"] = message.text;
		}
	}

	// Original text for edited messages
	if (action == MessageAction::Edited && !message.originalText.isEmpty()) {
		obj["original_text"] = message.originalText;
		obj["was_edited"] = true;
	}

	// Media information
	if (!message.mediaType.isEmpty()) {
		obj["media_type"] = message.mediaType;
		if (!message.fileName.isEmpty()) {
			obj["file_name"] = message.fileName;
		}
	}

	// Reply/quote information
	if (message.replyToMessageId > 0) {
		QJsonObject reply;
		reply["message_id"] = QString::number(message.replyToMessageId);
		reply["peer_id"] = QString::number(message.replyToPeerId);
		if (!message.replyToText.isEmpty()) {
			reply["quoted_text"] = message.replyToText;
		}
		obj["reply_to"] = reply;
	}

	obj["out"] = message.isOutgoing;

	return obj;
}

QString MessageLogger::escapeText(const QString &text) {
	QString result = text;
	result.replace('&', "&amp;");
	result.replace('<', "&lt;");
	result.replace('>', "&gt;");
	result.replace('"', "&quot;");
	result.replace('\'', "&#39;");

	// Limit length to prevent extremely long log entries
	if (result.length() > 2000) {
		result = result.left(1997) + "...";
	}
	return result;
}

void MessageLogger::initializeTracking() {
	QMutexLocker locker(&_trackingMutex);
	if (_trackingInitialized) {
		return;
	}
	_trackingInitialized = true;
	_originalTexts.clear();
	_logEntryIds.clear();
}

void MessageLogger::shutdownTracking() {
	QMutexLocker locker(&_trackingMutex);
	_originalTexts.clear();
	_logEntryIds.clear();
	_trackingInitialized = false;
}

void MessageLogger::trackMessage(not_null<HistoryItem*> item) {
	QMutexLocker locker(&_trackingMutex);
	if (!_trackingInitialized) {
		return;
	}

	const auto fullId = item->fullId();
	const auto text = item->isService()
		? item->notificationText().text
		: item->originalText().text;

	_originalTexts[fullId] = text;

	// Store log entry ID for this message (will be set when message is logged)
	if (!_logEntryIds.contains(fullId)) {
		_logEntryIds[fullId] = generateLogEntryId();
	}
}

void MessageLogger::updateTrackedMessage(not_null<HistoryItem*> item, const QString &originalText) {
	QMutexLocker locker(&_trackingMutex);
	if (!_trackingInitialized) {
		return;
	}

	const auto fullId = item->fullId();
	_originalTexts[fullId] = originalText;
}

void MessageLogger::removeTrackedMessage(not_null<HistoryItem*> item) {
	QMutexLocker locker(&_trackingMutex);
	if (!_trackingInitialized) {
		return;
	}

	const auto fullId = item->fullId();
	_originalTexts.remove(fullId);
	// Keep log entry ID for potential future reference, don't remove it
}

QString MessageLogger::getOriginalText(FullMsgId fullId) {
	QMutexLocker locker(&_trackingMutex);
	if (!_trackingInitialized) {
		return QString();
	}
	return _originalTexts.value(fullId);
}

void MessageLogger::updateTrackedText(FullMsgId fullId, const QString &newText) {
	QMutexLocker locker(&_trackingMutex);
	if (!_trackingInitialized) {
		return;
	}
	_originalTexts[fullId] = newText;
}

QString MessageLogger::getOriginalLogEntryId(FullMsgId fullId) {
	QMutexLocker locker(&_trackingMutex);
	if (!_trackingInitialized) {
		return QString();
	}
	return _logEntryIds.value(fullId);
}

QMutex& MessageLogger::getTrackingMutex() {
	return _trackingMutex;
}

QString MessageLogger::generateLogEntryId() {
	// Generate unique ID based on timestamp and random component
	const auto timestamp = QDateTime::currentMSecsSinceEpoch();
	const auto random = QRandomGenerator::global()->generate();
	return QString("log_%1_%2").arg(timestamp).arg(random);
}

QString MessageLogger::formatFullMsgId(FullMsgId fullId) {
	return QString("peer_%1_msg_%2").arg(fullId.peer.value).arg(fullId.msg.bare);
}

QList<LoggedMessage> MessageLogger::getMessageHistory(FullMsgId fullId) {
	QList<LoggedMessage> history;

	if (!_trackingInitialized) {
		// Don't return early, try to read the file anyway
	}

	const auto logDir = getLogDirectory();
	const auto jsonPath = logDir + "/messages.json";

	QFile file(jsonPath);
	if (!file.exists() || !file.open(QIODevice::ReadOnly)) {
		return history;
	}

	const auto data = file.readAll();
	file.close();

	const auto doc = QJsonDocument::fromJson(data);
	if (!doc.isObject()) {
		return history;
	}

	const auto root = doc.object();
	if (!root.contains("messages") || !root["messages"].isArray()) {
		return history;
	}

	const auto messages = root["messages"].toArray();
	const auto targetPeerId = QString::number(fullId.peer.value);
	const auto targetMsgId = QString::number(fullId.msg.bare);

	// Find all messages related to this message ID and peer ID
	for (const auto &value : messages) {
		if (!value.isObject()) {
			continue;
		}

		const auto messageObj = value.toObject();
		const auto peerId = messageObj["peer_id"].toString();
		const auto msgId = messageObj["message_id"].toString();

		if (peerId == targetPeerId && msgId == targetMsgId) {

			LoggedMessage loggedMsg;

			// Parse the JSON back to LoggedMessage
			// Handle both old and new field names
			loggedMsg.id = messageObj["id"].toString().toInt();
			loggedMsg.messageId = messageObj["message_id"].toString().toInt();
			loggedMsg.peerId = messageObj["peer_id"].toString().toLongLong();

			// Handle date fields - try different formats
			if (messageObj.contains("date")) {
				if (messageObj["date"].isString()) {
					// Parse date string
					const auto dateStr = messageObj["date"].toString();
					const auto dateTime = QDateTime::fromString(dateStr, Qt::ISODate);
					loggedMsg.date = dateTime.isValid() ? dateTime.toSecsSinceEpoch() : 0;
				} else {
					loggedMsg.date = messageObj["date"].toInt();
				}
			}

			loggedMsg.edited = messageObj["edited"].toInt();
			loggedMsg.chatName = messageObj["chat_name"].toString();
			loggedMsg.chatType = messageObj["chat_type"].toString();
			loggedMsg.chatId = messageObj["chat_id"].toString().toLongLong();
			loggedMsg.fromName = messageObj["from_name"].toString();
			loggedMsg.fromId = messageObj["from_id"].toString().toLongLong();
			loggedMsg.text = messageObj["text"].toString();
			loggedMsg.originalText = messageObj["original_text"].toString();
			loggedMsg.deletedText = messageObj["deleted_text"].toString();
			loggedMsg.mediaType = messageObj["media_type"].toString();
			loggedMsg.fileName = messageObj["file_name"].toString();
			loggedMsg.isService = messageObj["is_service"].toBool();
			loggedMsg.isOutgoing = messageObj["is_outgoing"].toBool();

			// Handle action field to determine if deleted/edited
			const auto action = messageObj["action"].toString();
			loggedMsg.isDeleted = (action == "deleted") || messageObj["is_deleted"].toBool();
			loggedMsg.isEdited = (action == "edited") || messageObj["is_edited"].toBool();

			loggedMsg.editReason = messageObj["edit_reason"].toString();

			// Handle action_date field
			if (messageObj.contains("action_date")) {
				if (messageObj["action_date"].isString()) {
					const auto dateStr = messageObj["action_date"].toString();
					const auto dateTime = QDateTime::fromString(dateStr, Qt::ISODate);
					loggedMsg.actionDate = dateTime.isValid() ? dateTime.toSecsSinceEpoch() : 0;
				} else {
					loggedMsg.actionDate = messageObj["action_date"].toInt();
				}
			}

			loggedMsg.logEntryId = messageObj["log_entry_id"].toString();
			loggedMsg.relatedLogEntryId = messageObj["related_log_entry_id"].toString();
			loggedMsg.replyToMessageId = messageObj["reply_to_message_id"].toString().toInt();
			loggedMsg.replyToPeerId = messageObj["reply_to_peer_id"].toString().toLongLong();
			loggedMsg.replyToText = messageObj["reply_to_text"].toString();

			history.append(loggedMsg);
		}
	}

	// Sort by action date (newest first)
	std::sort(history.begin(), history.end(), [](const LoggedMessage &a, const LoggedMessage &b) {
		// If action dates are the same, use regular date
		const auto dateA = a.actionDate > 0 ? a.actionDate : a.date;
		const auto dateB = b.actionDate > 0 ? b.actionDate : b.date;
		return dateA > dateB;
	});

	return history;
}

bool MessageLogger::hasMessageHistory(FullMsgId fullId) {
	const auto history = getMessageHistory(fullId);
	return history.size() > 1 ||
		   (history.size() == 1 && (history.first().isEdited || history.first().isDeleted));
}

LoggedMessage MessageLogger::convertFromMTPMessage(const MTPMessage &message, PeerId peerId, MessageAction action, not_null<Main::Session*> session) {
	LoggedMessage result;

	message.match([&](const MTPDmessage &data) {
		result.messageId = data.vid().v;
		result.peerId = peerId.value;
		result.date = data.vdate().v;
		result.edited = data.vedit_date().value_or(0);
		result.text = qs(data.vmessage());
		result.isOutgoing = data.is_out();

		// Get chat information
		result.chatName = getChatName(peerId, session);
		result.chatType = getChatType(peerId, session);
		result.chatId = peerId.value;

		// Add debug info about the source
		if (action == MessageAction::Edited) {
			result.text += QString(" [API Update Edit - Original date: %1, Edit date: %2]")
				.arg(QDateTime::fromSecsSinceEpoch(result.date).toString())
				.arg(result.edited > 0 ? QDateTime::fromSecsSinceEpoch(result.edited).toString() : "unknown");
		}

		// Get sender information
		if (data.vfrom_id()) {
			const auto fromId = peerFromMTP(*data.vfrom_id());
			result.fromId = fromId.value;
			result.fromName = getChatName(fromId, session);
		} else {
			result.fromId = peerId.value;
			result.fromName = result.chatName;
		}

		// Handle media
		if (data.vmedia()) {
			data.vmedia()->match([&](const MTPDmessageMediaPhoto &photo) {
				result.mediaType = "photo";
			}, [&](const MTPDmessageMediaDocument &doc) {
				result.mediaType = "document";
				if (doc.vdocument() && doc.vdocument()->type() == mtpc_document) {
					const auto &document = doc.vdocument()->c_document();
					for (const auto &attr : document.vattributes().v) {
						attr.match([&](const MTPDdocumentAttributeFilename &filename) {
							result.fileName = qs(filename.vfile_name());
						}, [](const auto &) {});
					}
				}
			}, [&](const MTPDmessageMediaContact &) {
				result.mediaType = "contact";
			}, [&](const MTPDmessageMediaGeo &) {
				result.mediaType = "location";
			}, [&](const MTPDmessageMediaVenue &) {
				result.mediaType = "venue";
			}, [&](const auto &) {
				result.mediaType = "other";
			});
		}

		// Handle reply information
		if (data.vreply_to()) {
			data.vreply_to()->match([&](const MTPDmessageReplyHeader &reply) {
				if (reply.vreply_to_msg_id()) {
					result.replyToMessageId = reply.vreply_to_msg_id()->v;
					result.replyToPeerId = peerId.value; // Same chat by default
				}
				if (reply.vreply_to_peer_id()) {
					result.replyToPeerId = peerFromMTP(*reply.vreply_to_peer_id()).value;
				}
			}, [](const auto &) {});
		}

	}, [&](const MTPDmessageService &data) {
		result.messageId = data.vid().v;
		result.peerId = peerId.value;
		result.date = data.vdate().v;
		result.isService = true;
		result.isOutgoing = data.is_out();

		// Get chat information
		result.chatName = getChatName(peerId, session);
		result.chatType = getChatType(peerId, session);
		result.chatId = peerId.value;

		// Get sender information
		if (data.vfrom_id()) {
			const auto fromId = peerFromMTP(*data.vfrom_id());
			result.fromId = fromId.value;
			result.fromName = getChatName(fromId, session);
		} else {
			result.fromId = peerId.value;
			result.fromName = result.chatName;
		}

		// Service message text (simplified)
		result.text = "[Service message]";

	}, [&](const MTPDmessageEmpty &) {
		// Empty message, minimal info
		result.peerId = peerId.value;
		result.chatName = getChatName(peerId, session);
		result.chatType = getChatType(peerId, session);
		result.chatId = peerId.value;
		result.text = "[Empty message]";
	});

	// Set log entry ID
	result.logEntryId = generateLogEntryId();

	// For edits and deletes, try to link to original
	if (action != MessageAction::Added) {
		const auto fullId = FullMsgId(peerId, result.messageId);
		result.relatedLogEntryId = getOriginalLogEntryId(fullId);
	}

	return result;
}

LoggedMessage MessageLogger::createDeletedMessageFromId(MsgId messageId, PeerId peerId, not_null<Main::Session*> session) {
	LoggedMessage result;

	result.messageId = messageId.bare;
	result.peerId = peerId.value;
	result.date = QDateTime::currentSecsSinceEpoch(); // Deletion time, not original message time

	// Get chat information
	result.chatName = getChatName(peerId, session);
	result.chatType = getChatType(peerId, session);
	result.chatId = peerId.value;

	// Try to get sender information from existing message if available
	const auto fullId = FullMsgId(peerId, messageId);
	if (session) {
		if (const auto existingItem = session->data().message(fullId)) {
			// Message is actually in memory, get real info
			if (const auto from = existingItem->from()) {
				result.fromId = from->id.value;
				result.fromName = getChatName(from->id, session);
			}
			result.date = existingItem->date(); // Use original message date
		}
	}

	// Fallback for unknown sender
	if (result.fromId == 0) {
		result.fromId = 0;
		result.fromName = "[Unknown - message was not in memory]";
	}

	// Set log entry ID
	result.logEntryId = generateLogEntryId();

	// Try to link to original
	result.relatedLogEntryId = getOriginalLogEntryId(fullId);

	// Add helpful information about the deletion
	result.text = QString("[Message ID %1 was deleted from %2 at %3]")
		.arg(messageId.bare)
		.arg(result.chatName)
		.arg(QDateTime::fromSecsSinceEpoch(result.actionDate).toString("yyyy-MM-dd hh:mm:ss"));

	return result;
}

QString MessageLogger::getChatName(PeerId peerId, not_null<Main::Session*> session) {
	if (!peerId || peerId.value == 0) {
		return "Unknown Chat";
	}

	// Try to get real chat name from session data
	if (session) {
		if (const auto peer = session->data().peerLoaded(peerId)) {
			if (const auto user = peer->asUser()) {
				return user->name();
			} else if (const auto chat = peer->asChat()) {
				return chat->name();
			} else if (const auto channel = peer->asChannel()) {
				return channel->name();
			}
		}
	}

	// Fallback to descriptive name with peer ID
	if (peerIsUser(peerId)) {
		return QString("Private Chat (ID: %1)").arg(peerId.value);
	} else if (peerIsChat(peerId)) {
		return QString("Group Chat (ID: %1)").arg(peerId.value);
	} else if (peerIsChannel(peerId)) {
		return QString("Channel/Supergroup (ID: %1)").arg(peerId.value);
	}
	return QString("Unknown Chat (ID: %1)").arg(peerId.value);
}

QString MessageLogger::getChatType(PeerId peerId, not_null<Main::Session*> session) {
	if (!peerId || peerId.value == 0) {
		return "unknown";
	}

	// Try to get more specific type from session data
	if (session) {
		if (const auto peer = session->data().peerLoaded(peerId)) {
			if (peer->isUser()) {
				return "private";
			} else if (peer->isChat()) {
				return "group";
			} else if (const auto channel = peer->asChannel()) {
				return channel->isMegagroup() ? "supergroup" : "channel";
			}
		}
	}

	// Fallback to basic type detection
	if (peerIsUser(peerId)) {
		return "private";
	} else if (peerIsChat(peerId)) {
		return "group";
	} else if (peerIsChannel(peerId)) {
		return "channel";
	}
	return "unknown";
}

void MessageLogger::logDebugMessage(const QString &debugText) {
	if (!Core::App().settings().messageLoggingEnabled()) {
		return;
	}

	try {
		LoggedMessage debugMessage;
		debugMessage.messageId = 999999;
		debugMessage.peerId = 0;
		debugMessage.date = QDateTime::currentSecsSinceEpoch();
		debugMessage.text = QString("[DEBUG] %1").arg(debugText);
		debugMessage.chatName = "Debug Log";
		debugMessage.chatType = "debug";
		debugMessage.fromName = "System";
		debugMessage.logEntryId = generateLogEntryId();
		debugMessage.actionDate = QDateTime::currentSecsSinceEpoch();

		writeHtmlLog(debugMessage, MessageAction::Added);
		writeJsonLog(debugMessage, MessageAction::Added);

		LOG(("MessageLogger: Debug message logged: %1").arg(debugText));
	} catch (...) {
		LOG(("MessageLogger: Error logging debug message"));
	}
}

} // namespace Data

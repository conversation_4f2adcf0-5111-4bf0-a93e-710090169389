<!DOCTYPE html>
<html>
<head>
    <title>Тест схемы tg://search</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>Тест схемы tg://search</h1>
    <p>Нажмите на ссылки ниже, чтобы протестировать новую схему поиска:</p>
    
    <h2>Примеры ссылок:</h2>
    <ul>
        <li><a href="tg://search?domain=durov&text=hello">Поиск "hello" в чате @durov</a></li>
        <li><a href="tg://search?domain=telegram&text=update">Поиск "update" в чате @telegram</a></li>
        <li><a href="tg://search?domain=username123&text=test message">Поиск "test message" в чате @username123</a></li>
    </ul>
    
    <h2>Формат схемы:</h2>
    <code>tg://search?domain=USERNAME&text=SEARCH_TEXT</code>
    
    <h2>Параметры:</h2>
    <ul>
        <li><strong>domain</strong> - username пользователя или канала (без @)</li>
        <li><strong>text</strong> - текст для поиска в чате</li>
    </ul>
    
    <p><em>Примечание: Для работы ссылок необходимо, чтобы Telegram Desktop был установлен и зарегистрирован как обработчик протокола tg://</em></p>
</body>
</html>

# Логирование сообщений в Telegram Desktop

## Описание

Добавлена функция логирования всех полученных сообщений в форматах HTML и JSON, аналогично экспорту истории чатов Telegram. Все входящие сообщения автоматически записываются в два файла: `messages.html` и `messages.json` в папке документов пользователя.

**Новая функциональность**: Добавлено отслеживание редактирования и удаления сообщений с сохранением оригинального текста и временных меток изменений.

**Последнее обновление**: Добавлено логирование изменений сообщений из API updates - теперь система логирует редактирование и удаление сообщений даже если они не находятся в памяти клиента (далеко в истории чата).

## Реализованные изменения

### 1. Расширение системы логирования (logs.h/logs.cpp)
- Добавлен новый тип логирования `LogDataMessages`
- Добавлена функция `writeMessages()` для записи сообщений
- Добавлен макрос `MESSAGE_LOG()` для совместимости

### 2. Класс MessageLogger (data_message_logger.h/cpp)
- `MessageLogger::logMessage()` - основная функция логирования новых сообщений
- `MessageLogger::logMessageEdited()` - логирование редактированных сообщений
- `MessageLogger::logMessageDeleted()` - логирование удаленных сообщений
- `LoggedMessage` структура - унифицированное представление сообщения с поддержкой отслеживания изменений
- `writeHtmlLog()` и `writeJsonLog()` - запись в соответствующие форматы с учетом типа действия
- `escapeText()` - экранирование HTML символов
- Система отслеживания сообщений для детекции изменений

### 3. Интеграция в data_session.cpp
- Добавлен вызов логирования в функцию `addNewMessage()`
- Логируются все входящие сообщения (включая служебные)
- Добавлена система отслеживания изменений сообщений через `setupMessageTrackingViewer()`
- Автоматическое отслеживание событий редактирования и удаления через систему изменений Telegram Desktop
- **НОВОЕ**: Добавлено логирование из API updates в `updateEditedMessage()`, `processMessagesDeleted()` и `processNonChannelMessagesDeleted()`

### 4. Логирование из API Updates
- `logMessageEditedFromUpdate()` - логирование редактирования сообщений не в памяти
- `logMessageDeletedFromUpdate()` - логирование удаления сообщений не в памяти
- `convertFromMTPMessage()` - конвертация MTP сообщений в LoggedMessage
- `getChatName()` и `getChatType()` - получение реальных имен чатов из сессии

## Что теперь логируется

### Сообщения в памяти (как раньше)
- Новые входящие сообщения
- Редактирование сообщений с сохранением оригинального текста
- Удаление сообщений с сохранением удаленного текста

### Сообщения НЕ в памяти (НОВОЕ!)
- **Редактирование старых сообщений** - даже если сообщение находится далеко в истории чата
- **Удаление старых сообщений** - даже если сообщение находится далеко в истории чата
- **Работает в реальном времени** - если вы онлайн, все изменения логируются мгновенно
- **Получение реальных имен чатов** - система пытается получить настоящие имена чатов из сессии

### Ограничения
- Для сообщений не в памяти: если оригинальный текст неизвестен, отображается `[Original text unknown - message not in memory]`
- Для удаленных сообщений не в памяти: если содержимое неизвестно, отображается `[Deleted message content unknown - message not in memory]`
- Оффлайн изменения пока не обрабатываются (планируется в будущем)

## Форматы логирования

### HTML формат (messages.html)
Создается красиво оформленная HTML страница в стиле экспорта Telegram:
- Адаптивный дизайн с CSS стилями
- Разделение на обычные и служебные сообщения
- Выделение исходящих сообщений
- Информация о медиафайлах
- Временные метки и информация о редактировании

### JSON формат (messages.json)
Структурированные данные в формате JSON, совместимом с экспортом Telegram:
```json
{
  "about": "Telegram Desktop Messages Log",
  "export_date": "2024-01-15 14:30:25",
  "messages": [
    {
      "id": "12345",
      "type": "message",
      "date": "2024-01-15 14:30:25",
      "date_unixtime": "1705317025",
      "chat": {
        "id": "123456789",
        "name": "Мой друг",
        "type": "private_chat"
      },
      "from": {
        "id": "987654321",
        "name": "Иван Иванов"
      },
      "text": "Привет! Как дела?",
      "out": false
    }
  ]
}
```

## Особенности

1. **Полное логирование**: Логируются все входящие сообщения, включая служебные
2. **Двойной формат**: Одновременная запись в HTML и JSON форматы
3. **Отслеживание изменений**: Автоматическое отслеживание редактирования и удаления сообщений
4. **Сохранение истории**: Оригинальный текст сохраняется при редактировании
5. **Временные метки**: Точное время создания, редактирования и удаления
6. **Экранирование HTML**: Корректная обработка специальных символов для HTML
7. **Информация о медиа**: Определение типа медиафайлов (фото, видео, аудио и т.д.)
8. **Структурированные данные**: JSON формат совместим с экспортом Telegram
9. **Потокобезопасность**: Использование мьютексов для корректной записи
10. **Визуальное выделение**: Специальные CSS стили для отредактированных и удаленных сообщений

## Расположение файлов

Файлы логирования создаются в папке:
```
Документы/Telegram Desktop/message_logs/
├── messages.html    # HTML версия с красивым оформлением
└── messages.json    # JSON версия для программной обработки
```

## Файлы изменений

### Новые файлы:
- `Telegram/SourceFiles/data/data_message_logger.h`
- `Telegram/SourceFiles/data/data_message_logger.cpp`
- `Telegram/SourceFiles/tests/test_message_logger.h`
- `Telegram/SourceFiles/tests/test_message_logger.cpp`

### Измененные файлы:
- `Telegram/SourceFiles/logs.h` - добавлена функция `writeMessages()` и макрос `MESSAGE_LOG()`
- `Telegram/SourceFiles/logs.cpp` - реализация логирования сообщений
- `Telegram/SourceFiles/data/data_session.cpp` - интеграция логирования в обработку сообщений
- `Telegram/CMakeLists.txt` - добавлены новые файлы в сборку
- `Telegram/cmake/tests.cmake` - добавлены тесты

## Использование

После компиляции и запуска приложения:

1. Все входящие сообщения автоматически записываются в HTML и JSON файлы
2. Файлы создаются в папке документов пользователя
3. Логирование работает в реальном времени
4. HTML файл можно открыть в браузере для просмотра
5. JSON файл можно обрабатывать программно

## Отключение логирования

Чтобы отключить логирование сообщений, закомментируйте строки в `data_session.cpp`:
```cpp
// writeHtmlLog(message);
// writeJsonLog(message);
```

## Тестирование

Добавлены тесты для проверки функциональности:
- Тест экранирования HTML символов
- Тест работы макроса логирования
- Тест структуры LoggedMessage

## Примечания

- Логирование работает для всех входящих сообщений
- Служебные сообщения логируются с пометкой "service"
- Файлы логирования создаются автоматически при первом сообщении
- Логирование минимально влияет на производительность приложения
- Поддерживается определение типов медиафайлов

## Компиляция

После внесения всех изменений, скомпилируйте проект как обычно. Новая функциональность будет автоматически включена.

## Проверка работы

1. Запустите скомпилированный Telegram Desktop
2. Получите несколько сообщений
3. Проверьте наличие файлов в папке `Документы/Telegram Desktop/message_logs/`
4. Откройте `messages.html` в браузере для просмотра
5. Проверьте `messages.json` для программной обработки

## Дополнительные файлы

- `EXAMPLE_USAGE.md` - подробные примеры использования и анализа логов
- `tests/test_message_logger.*` - тесты для проверки функциональности

# Решение проблемы логирования реакций как редактирований

## Проблема
В системе логирования Telegram Desktop любая реакция (смайлик) на сообщение ошибочно расценивалась как редактирование сообщения и отображалась в логах как изменение текста.

## Анализ причины
После изучения кода выяснилось, что в Telegram Desktop есть два типа обновлений сообщений:

1. **`updateMessageReactions`** - только для реакций, НЕ генерирует `MessageUpdate::Flag::Edited`
2. **`updateEditMessage`** - для реальных редактирований, генерирует `MessageUpdate::Flag::Edited`

Однако иногда изменения реакций могут приводить к генерации `MessageUpdate::Flag::Edited`, что вызывало ложные срабатывания в системе логирования.

## Решение
Добавлена функция `isRealMessageEdit()` в класс `Session`, которая:

1. **Сравнивает текст сообщения** с ранее сохраненной версией
2. **Возвращает `true`** только если текст действительно изменился
3. **Возвращает `false`** если изменились только реакции/просмотры

### Измененные файлы:

#### `data_session.h`
```cpp
bool isRealMessageEdit(not_null<HistoryItem*> item) const;
```

#### `data_session.cpp`
```cpp
// В setupMessageTrackingViewer()
if (isRealMessageEdit(item)) {
    // Логируем только реальные редактирования
}

// Новая функция
bool Session::isRealMessageEdit(not_null<HistoryItem*> item) const {
    // Сравниваем текущий и предыдущий текст
    // Возвращаем true только если текст изменился
}
```

## Результат
- ✅ Реакции больше не логируются как редактирования
- ✅ Реальные изменения текста продолжают корректно логироваться  
- ✅ Обратная совместимость сохранена
- ✅ Минимальные изменения в коде

## Тестирование
1. Включить логирование сообщений
2. Отправить сообщение
3. Поставить реакцию → НЕ должно появиться в логах
4. Отредактировать текст → ДОЛЖНО появиться в логах

## Техническая элегантность
Хотя мы рассматривали более сложные решения на уровне MTP протокола, выбранное решение:
- Простое и понятное
- Надежное (работает независимо от изменений в протоколе)
- Эффективное (минимальные вычислительные затраты)
- Безопасное (не влияет на другую функциональность)

Решение готово к использованию!

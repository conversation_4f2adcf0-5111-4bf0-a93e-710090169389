# Изменение порядка сообщений в HTML логах

## Проблема
В HTML логах сообщений новые сообщения добавлялись в конец файла, что означало, что для просмотра последних сообщений нужно было прокручивать страницу вниз.

## Решение
Изменен порядок отображения сообщений в HTML логах - теперь новые сообщения появляются сверху (в обратном хронологическом порядке).

### Изменения в коде

#### 1. Изменена логика вставки в HTML (`writeHtmlLog`)

**Было:**
```cpp
// Insert new message before closing tags
const auto insertPos = content.lastIndexOf("    </div>\n  </div>\n</body>");
if (insertPos != -1) {
    content.insert(insertPos, formatHtmlMessage(message, action) + "\n");
}
```

**Стало:**
```cpp
// Insert new message at the beginning of message history (newest first)
const auto insertPos = content.indexOf("    <div class=\"history\" id=\"message_history\">\n");
if (insertPos != -1) {
    const auto afterHistoryTag = insertPos + QString("    <div class=\"history\" id=\"message_history\">\n").length();
    content.insert(afterHistoryTag, formatHtmlMessage(message, action) + "\n");
}
```

#### 2. Изменена логика JSON логирования

**Было:**
```cpp
// Add new message
messages.append(formatJsonMessage(message, action));
```

**Стало:**
```cpp
// Add new message at the beginning (newest first)
messages.prepend(formatJsonMessage(message, action));
```

#### 3. Добавлены пояснения в интерфейс

- Добавлен комментарий в CSS о порядке сообщений
- Добавлено пояснение в заголовок HTML страницы: "Messages are displayed in reverse chronological order (newest first)"

### Логика работы

1. **HTML логи**: Новые сообщения вставляются сразу после открывающего тега `<div class="history" id="message_history">`
2. **JSON логи**: Новые сообщения добавляются в начало массива `messages` с помощью `prepend()`
3. **Fallback логика**: Если структура HTML изменилась, есть несколько уровней fallback для корректной вставки

### Преимущества

- ✅ Новые сообщения сразу видны при открытии лога
- ✅ Не нужно прокручивать страницу для просмотра последних сообщений
- ✅ Логика работает как для HTML, так и для JSON логов
- ✅ Сохранена обратная совместимость с существующими логами
- ✅ Фильтрация и поиск продолжают работать корректно

### Совместимость

- **Существующие логи**: Продолжают работать без изменений
- **Новые сообщения**: Добавляются в правильном порядке (сверху)
- **Фильтрация**: JavaScript фильтры работают независимо от порядка сообщений

## Тестирование

Для проверки работы:
1. Включить логирование сообщений в настройках
2. Отправить несколько сообщений
3. Открыть HTML лог - новые сообщения должны появляться сверху
4. Проверить JSON лог - новые сообщения должны быть в начале массива

## Результат

Теперь HTML логи отображают сообщения в более удобном порядке - новые сообщения сверху, что соответствует ожиданиям пользователей и делает просмотр логов более удобным.
